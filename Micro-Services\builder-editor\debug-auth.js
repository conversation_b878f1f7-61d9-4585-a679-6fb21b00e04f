const { createClient } = require('@supabase/supabase-js')

const supabaseUrl = 'https://cikzkzviubwpruiowapp.supabase.co'
const supabaseKey = 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNpa3prenZpdWJ3cHJ1aW93YXBwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2NDkyNTMsImV4cCI6MjA2NDIyNTI1M30.3maWsFX4bztpRlT1MzXSsxj_9ESaA6doWOYUtxPGOrI'

async function debugAuth() {
  console.log('🔍 Starting authentication debug...')
  
  const supabase = createClient(supabaseUrl, supabaseKey)
  
  console.log('✅ Supabase client created')
  
  // Test 1: Check connection
  try {
    const { data, error } = await supabase
      .from('websites')
      .select('count')
      .limit(1)
    
    console.log('❌ Unauthenticated query (should fail):', { data, error })
  } catch (err) {
    console.log('❌ Unauthenticated query error:', err.message)
  }
  
  // Test 2: Create demo user
  const demoEmail = '<EMAIL>'
  const demoPassword = 'debug123456'
  
  console.log('\n🔐 Testing authentication...')
  
  try {
    // Try sign up
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: demoEmail,
      password: demoPassword,
      options: {
        data: {
          full_name: 'Debug User'
        }
      }
    })
    
    console.log('📝 Sign up result:', { 
      user: signUpData?.user?.email, 
      error: signUpError?.message 
    })
    
    // Try sign in
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: demoEmail,
      password: demoPassword
    })
    
    console.log('🔑 Sign in result:', { 
      user: signInData?.user?.email, 
      userId: signInData?.user?.id,
      error: signInError?.message 
    })
    
    if (signInData?.user) {
      console.log('\n✅ User authenticated successfully!')
      
      // Test 3: Try creating website with authenticated user
      const websiteData = {
        user_id: signInData.user.id,
        name: 'Debug Test Website',
        language: 'en',
        theme: {
          primaryColor: '#009882',
          secondaryColor: '#6c757d',
          fontFamily: 'Inter, sans-serif'
        },
        settings: {
          rtl: false,
          showHeader: true,
          showFooter: true
        }
      }
      
      console.log('\n🏗️ Testing website creation...')
      
      const { data: websiteResult, error: websiteError } = await supabase
        .from('websites')
        .insert(websiteData)
        .select()
        .single()
      
      console.log('🌐 Website creation result:', { 
        website: websiteResult?.name, 
        websiteId: websiteResult?.id,
        error: websiteError?.message,
        fullError: websiteError
      })
      
      if (websiteError) {
        console.log('❌ Full error details:', JSON.stringify(websiteError, null, 2))
      } else {
        console.log('✅ Website created successfully!')
      }
    }
    
  } catch (err) {
    console.log('❌ Authentication error:', err.message)
  }
}

debugAuth().catch(console.error)
