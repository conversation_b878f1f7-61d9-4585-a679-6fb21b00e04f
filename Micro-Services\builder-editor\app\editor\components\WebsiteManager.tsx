'use client'

import React, { useState, useEffect } from 'react'
import {
  Box,
  Button,
  VStack,
  HStack,
  Text,
  Input,
  Select,
  Modal,
  ModalOverlay,
  ModalContent,
  ModalHeader,
  ModalBody,
  ModalFooter,
  ModalCloseButton,
  useDisclosure,
  useToast,
  Spinner,
  Card,
  CardBody,
  Badge,
  IconButton
} from '@chakra-ui/react'
import { AddIcon, EditIcon, ExternalLinkIcon } from '@chakra-ui/icons'
import { useEditorStore } from '@/lib/stores/editorStore'
import { createWebsite, loadUserWebsites, DatabaseWebsite, supabase } from '@/lib/supabase/sections'

interface WebsiteManagerProps {
  userId: string
  onWebsiteSelected: (websiteId: string) => void
}

export function WebsiteManager({ userId, onWebsiteSelected }: WebsiteManagerProps) {
  const [websites, setWebsites] = useState<DatabaseWebsite[]>([])
  const [loading, setLoading] = useState(true)
  const [creating, setCreating] = useState(false)
  const [newWebsiteName, setNewWebsiteName] = useState('')
  const [newWebsiteLanguage, setNewWebsiteLanguage] = useState<'en' | 'ar'>('en')
  
  const { isOpen, onOpen, onClose } = useDisclosure()
  const toast = useToast()
  const { setCurrentWebsite, loadFromSupabase } = useEditorStore()

  useEffect(() => {
    loadWebsites()
  }, [userId])

  const loadWebsites = async () => {
    setLoading(true)
    try {
      const result = await loadUserWebsites(userId)
      if (result.error) {
        toast({
          title: 'Error loading websites',
          description: result.error,
          status: 'error',
          duration: 5000,
          isClosable: true,
        })
      } else {
        setWebsites(result.websites)
      }
    } catch (error) {
      toast({
        title: 'Error',
        description: 'Failed to load websites',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    } finally {
      setLoading(false)
    }
  }

  const handleCreateWebsite = async () => {
    if (!newWebsiteName.trim()) {
      toast({
        title: 'Error',
        description: 'Please enter a website name',
        status: 'error',
        duration: 3000,
        isClosable: true,
      })
      return
    }

    setCreating(true)
    try {
      // Direct Supabase call to avoid RLS issues for now
      const websiteData = {
        user_id: userId,
        name: newWebsiteName.trim(),
        language: newWebsiteLanguage,
        theme: {
          primaryColor: '#00d4aa',
          secondaryColor: '#6c757d',
          fontFamily: 'Inter, sans-serif'
        },
        settings: {
          rtl: newWebsiteLanguage === 'ar',
          showHeader: true,
          showFooter: true
        }
      }

      const { data, error } = await supabase
        .from('websites')
        .insert(websiteData)
        .select()
        .single()

      if (error) {
        console.error('Supabase error:', error)
        toast({
          title: 'Error creating website',
          description: error.message,
          status: 'error',
          duration: 5000,
          isClosable: true,
        })
      } else if (data) {
        toast({
          title: 'Website created',
          description: `${data.name} has been created successfully`,
          status: 'success',
          duration: 3000,
          isClosable: true,
        })
        setWebsites([data, ...websites])
        setNewWebsiteName('')
        setNewWebsiteLanguage('en')
        onClose()
        // Automatically select the new website
        onWebsiteSelected(data.id)
      }
    } catch (error) {
      console.error('Error creating website:', error)
      toast({
        title: 'Error',
        description: 'Failed to create website',
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    } finally {
      setCreating(false)
    }
  }

  const handleSelectWebsite = async (website: DatabaseWebsite) => {
    setCurrentWebsite(website.id)
    
    // Load sections for this website
    const result = await loadFromSupabase(website.id)
    if (result.error) {
      toast({
        title: 'Error loading website',
        description: result.error,
        status: 'error',
        duration: 5000,
        isClosable: true,
      })
    } else {
      toast({
        title: 'Website loaded',
        description: `${website.name} has been loaded successfully`,
        status: 'success',
        duration: 3000,
        isClosable: true,
      })
      onWebsiteSelected(website.id)
    }
  }

  if (loading) {
    return (
      <Box textAlign="center" py="40px">
        <Spinner size="lg" color="blue.500" />
        <Text mt="16px" color="gray.600">Loading your websites...</Text>
      </Box>
    )
  }

  return (
    <Box p="20px">
      <HStack justify="space-between" mb="20px">
        <Text fontSize="2xl" fontWeight="bold">Your Websites</Text>
        <Button
          leftIcon={<AddIcon />}
          colorScheme="blue"
          onClick={onOpen}
        >
          Create Website
        </Button>
      </HStack>

      {websites.length === 0 ? (
        <Box textAlign="center" py="40px">
          <Text fontSize="lg" color="gray.600" mb="16px">
            You don't have any websites yet
          </Text>
          <Button
            leftIcon={<AddIcon />}
            colorScheme="blue"
            onClick={onOpen}
          >
            Create Your First Website
          </Button>
        </Box>
      ) : (
        <VStack spacing="16px" align="stretch">
          {websites.map((website) => (
            <Card key={website.id} cursor="pointer" _hover={{ shadow: 'md' }}>
              <CardBody>
                <HStack justify="space-between">
                  <VStack align="start" spacing="8px">
                    <HStack>
                      <Text fontSize="lg" fontWeight="semibold">
                        {website.name}
                      </Text>
                      <Badge colorScheme={website.language === 'ar' ? 'green' : 'blue'}>
                        {website.language === 'ar' ? 'Arabic' : 'English'}
                      </Badge>
                    </HStack>
                    {website.domain && (
                      <HStack>
                        <Text fontSize="sm" color="gray.600">
                          {website.domain}
                        </Text>
                        <IconButton
                          aria-label="Visit website"
                          icon={<ExternalLinkIcon />}
                          size="xs"
                          variant="ghost"
                          onClick={(e) => {
                            e.stopPropagation()
                            window.open(`https://${website.domain}`, '_blank')
                          }}
                        />
                      </HStack>
                    )}
                    <Text fontSize="xs" color="gray.500">
                      Created: {new Date(website.created_at).toLocaleDateString()}
                    </Text>
                  </VStack>
                  <HStack>
                    <Button
                      leftIcon={<EditIcon />}
                      colorScheme="blue"
                      size="sm"
                      onClick={() => handleSelectWebsite(website)}
                    >
                      Edit
                    </Button>
                  </HStack>
                </HStack>
              </CardBody>
            </Card>
          ))}
        </VStack>
      )}

      {/* Create Website Modal */}
      <Modal isOpen={isOpen} onClose={onClose}>
        <ModalOverlay />
        <ModalContent>
          <ModalHeader>Create New Website</ModalHeader>
          <ModalCloseButton />
          <ModalBody>
            <VStack spacing="16px">
              <Box w="100%">
                <Text mb="8px" fontWeight="medium">Website Name</Text>
                <Input
                  placeholder="Enter website name"
                  value={newWebsiteName}
                  onChange={(e) => setNewWebsiteName(e.target.value)}
                />
              </Box>
              <Box w="100%">
                <Text mb="8px" fontWeight="medium">Language</Text>
                <Select
                  value={newWebsiteLanguage}
                  onChange={(e) => setNewWebsiteLanguage(e.target.value as 'en' | 'ar')}
                >
                  <option value="en">English</option>
                  <option value="ar">Arabic</option>
                </Select>
              </Box>
            </VStack>
          </ModalBody>
          <ModalFooter>
            <Button variant="ghost" mr={3} onClick={onClose}>
              Cancel
            </Button>
            <Button
              colorScheme="blue"
              onClick={handleCreateWebsite}
              isLoading={creating}
              loadingText="Creating..."
            >
              Create Website
            </Button>
          </ModalFooter>
        </ModalContent>
      </Modal>
    </Box>
  )
}
