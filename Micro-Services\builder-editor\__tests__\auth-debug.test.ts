import { describe, it, expect, beforeAll } from 'vitest'
import { createClient } from '@supabase/supabase-js'

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL || 'https://cikzkzviubwpruiowapp.supabase.co'
const supabaseKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || 'eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9.eyJpc3MiOiJzdXBhYmFzZSIsInJlZiI6ImNpa3prenZpdWJ3cHJ1aW93YXBwIiwicm9sZSI6ImFub24iLCJpYXQiOjE3NDg2NDkyNTMsImV4cCI6MjA2NDIyNTI1M30.3maWsFX4bztpRlT1MzXSsxj_9ESaA6doWOYUtxPGOrI'

describe('Authentication and Database Debug', () => {
  let supabase: any

  beforeAll(() => {
    supabase = createClient(supabaseUrl, supabaseKey)
  })

  it('should connect to Supabase', async () => {
    expect(supabase).toBeDefined()
    expect(supabaseUrl).toBe('https://cikzkzviubwpruiowapp.supabase.co')
    expect(supabaseKey).toBeDefined()
  })

  it('should check websites table access without auth', async () => {
    const { data, error } = await supabase
      .from('websites')
      .select('*')
      .limit(1)

    console.log('Unauthenticated query result:', { data, error })
    
    // Should fail due to RLS
    expect(error).toBeDefined()
    expect(data).toEqual([])
  })

  it('should create demo user and test authentication', async () => {
    const demoEmail = '<EMAIL>'
    const demoPassword = 'demo123456'

    // Try to sign up demo user
    const { data: signUpData, error: signUpError } = await supabase.auth.signUp({
      email: demoEmail,
      password: demoPassword,
      options: {
        data: {
          full_name: 'Demo User'
        }
      }
    })

    console.log('Sign up result:', { signUpData, signUpError })

    if (signUpError && !signUpError.message.includes('already registered')) {
      throw signUpError
    }

    // Try to sign in
    const { data: signInData, error: signInError } = await supabase.auth.signInWithPassword({
      email: demoEmail,
      password: demoPassword
    })

    console.log('Sign in result:', { signInData, signInError })

    if (signInError) {
      throw signInError
    }

    expect(signInData.user).toBeDefined()
    expect(signInData.user.email).toBe(demoEmail)
  })

  it('should test website creation with authenticated user', async () => {
    const demoEmail = '<EMAIL>'
    const demoPassword = 'demo123456'

    // Sign in first
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: demoEmail,
      password: demoPassword
    })

    if (authError) {
      throw authError
    }

    expect(authData.user).toBeDefined()

    // Now try to create a website
    const websiteData = {
      user_id: authData.user.id,
      name: 'Test Website',
      language: 'en',
      theme: {
        primaryColor: '#009882',
        secondaryColor: '#6c757d',
        fontFamily: 'Inter, sans-serif'
      },
      settings: {
        rtl: false,
        showHeader: true,
        showFooter: true
      }
    }

    const { data, error } = await supabase
      .from('websites')
      .insert(websiteData)
      .select()
      .single()

    console.log('Website creation result:', { data, error })

    if (error) {
      console.error('Website creation error details:', error)
    }

    expect(error).toBeNull()
    expect(data).toBeDefined()
    expect(data.name).toBe('Test Website')
    expect(data.user_id).toBe(authData.user.id)
  })

  it('should test auth.uid() function', async () => {
    const demoEmail = '<EMAIL>'
    const demoPassword = 'demo123456'

    // Sign in first
    const { data: authData, error: authError } = await supabase.auth.signInWithPassword({
      email: demoEmail,
      password: demoPassword
    })

    if (authError) {
      throw authError
    }

    // Test auth.uid() function
    const { data, error } = await supabase
      .rpc('auth_uid_test')

    console.log('auth.uid() test result:', { data, error })
    
    // This might fail if the function doesn't exist, but that's okay
    // The important thing is to see what happens
  })

  it('should check current user session', async () => {
    const { data: { user }, error } = await supabase.auth.getUser()
    
    console.log('Current user session:', { user, error })
    
    if (user) {
      expect(user.email).toBeDefined()
      console.log('User ID:', user.id)
      console.log('User email:', user.email)
    }
  })
})
